using UnityEngine;
using UnityEngine.EventSystems;
using System.Collections.Generic;

public class DraggableMaskWithDetection : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IDragHandler
{
    [Header("Mask Settings")]
    public RectTransform maskRect;        // The circular draggable mask
    public RectTransform staticImage;     // The static image that gets revealed by the mask

    [Header("Target Detection")]
    public RectTransform[] targets;       // The small UI elements to detect
    public GameObject[] relatedObjects;   // Objects to activate based on overlap
    [Range(0f, 1f)]
    public float coverageThreshold = 0.8f; // 80% coverage threshold

    [Header("Completion Settings")]
    public GameObject[] objectsToActivateOnCompletion;   // Objects to activate when all targets scanned
    public GameObject[] objectsToDeactivateOnCompletion; // Objects to deactivate when all targets scanned

    [Header("Control")]
    public bool isDraggingEnabled = false; // Controls whether dragging is allowed

    private Canvas canvas;
    private bool[] targetScanned;          // Track which targets have been scanned
    private bool[] targetSnapped;          // Track which targets have been snapped to (first time only)
    private HashSet<int> scannedTargets;   // Set of scanned target indices
    private bool allTargetsScanned = false;

    void Start()
    {
        canvas = GetComponentInParent<Canvas>();

        // Initialize tracking arrays
        targetScanned = new bool[targets.Length];
        targetSnapped = new bool[targets.Length];
        scannedTargets = new HashSet<int>();

        // Initially deactivate all related objects
        for (int i = 0; i < relatedObjects.Length; i++)
        {
            if (relatedObjects[i] != null)
                relatedObjects[i].SetActive(false);
        }
    }

    // Call this method from a button to enable dragging
    public void EnableDragging()
    {
        isDraggingEnabled = true;
    }

    // Call this method to disable dragging
    public void DisableDragging()
    {
        isDraggingEnabled = false;
    }

    public void OnDrag(PointerEventData eventData)
    {
        // Only allow dragging if enabled
        if (!isDraggingEnabled)
            return;

        // Move the mask to reveal different parts of the static image
        Vector2 deltaMovement = eventData.delta / canvas.scaleFactor;
        maskRect.anchoredPosition += deltaMovement;

        // If there's a static image child, move it in the opposite direction
        // This creates the effect of the mask revealing different parts of the image
        if (staticImage != null)
        {
            staticImage.anchoredPosition -= deltaMovement;
        }

        CheckCoverage();
    }

    void CheckCoverage()
    {
        for (int i = 0; i < targets.Length; i++)
        {
            float coveragePercentage = CalculateCoveragePercentage(maskRect, targets[i]);

            if (coveragePercentage >= coverageThreshold)
            {
                // Activate related object if not already active
                if (relatedObjects[i] != null && !relatedObjects[i].activeInHierarchy)
                {
                    relatedObjects[i].SetActive(true);
                }

                // Mark as scanned and add to scanned set
                if (!targetScanned[i])
                {
                    targetScanned[i] = true;
                    scannedTargets.Add(i);

                    // Snap to target on first scan only
                    if (!targetSnapped[i])
                    {
                        SnapToTarget(targets[i]);
                        targetSnapped[i] = true;
                    }
                }
            }
            else
            {
                // Deactivate related object if coverage drops below threshold
                if (relatedObjects[i] != null && relatedObjects[i].activeInHierarchy)
                {
                    relatedObjects[i].SetActive(false);
                }

                // Remove from scanned if coverage drops
                if (targetScanned[i])
                {
                    targetScanned[i] = false;
                    scannedTargets.Remove(i);
                }
            }
        }

        CheckAllTargetsScanned();
    }

    float CalculateCoveragePercentage(RectTransform mask, RectTransform target)
    {
        // Get world positions and sizes
        Vector3[] maskCorners = new Vector3[4];
        Vector3[] targetCorners = new Vector3[4];

        mask.GetWorldCorners(maskCorners);
        target.GetWorldCorners(targetCorners);

        // Convert to screen space for easier calculation
        Vector2 maskCenter = Camera.main.WorldToScreenPoint(mask.position);
        Vector2 targetCenter = Camera.main.WorldToScreenPoint(target.position);

        // Calculate mask radius (assuming circular mask)
        float maskRadius = Vector2.Distance(
            Camera.main.WorldToScreenPoint(maskCorners[0]),
            Camera.main.WorldToScreenPoint(maskCorners[2])
        ) * 0.5f;

        // Calculate target size
        Vector2 targetSize = new Vector2(
            Vector2.Distance(Camera.main.WorldToScreenPoint(targetCorners[0]), Camera.main.WorldToScreenPoint(targetCorners[3])),
            Vector2.Distance(Camera.main.WorldToScreenPoint(targetCorners[0]), Camera.main.WorldToScreenPoint(targetCorners[1]))
        );

        // Calculate overlap area (simplified circular-rectangular intersection)
        float distance = Vector2.Distance(maskCenter, targetCenter);

        // If mask completely contains target
        if (distance + Mathf.Max(targetSize.x, targetSize.y) * 0.5f <= maskRadius)
        {
            return 1.0f; // 100% coverage
        }

        // If no overlap
        if (distance > maskRadius + Mathf.Max(targetSize.x, targetSize.y) * 0.5f)
        {
            return 0.0f; // 0% coverage
        }

        // Approximate coverage calculation
        float maxDistance = maskRadius + Mathf.Max(targetSize.x, targetSize.y) * 0.5f;
        float minDistance = Mathf.Abs(maskRadius - Mathf.Max(targetSize.x, targetSize.y) * 0.5f);

        if (distance <= minDistance)
            return 1.0f;

        // Linear interpolation for partial coverage
        return Mathf.Clamp01(1.0f - (distance - minDistance) / (maxDistance - minDistance));
    }

    void SnapToTarget(RectTransform target)
    {
        // Snap mask to target position
        maskRect.position = target.position;
    }

    void CheckAllTargetsScanned()
    {
        bool allScanned = scannedTargets.Count >= targets.Length;

        if (allScanned && !allTargetsScanned)
        {
            allTargetsScanned = true;
            OnAllTargetsScanned();
        }
        else if (!allScanned && allTargetsScanned)
        {
            allTargetsScanned = false;
            OnNotAllTargetsScanned();
        }
    }

    void OnAllTargetsScanned()
    {
        // Activate completion objects
        foreach (GameObject obj in objectsToActivateOnCompletion)
        {
            if (obj != null)
                obj.SetActive(true);
        }

        // Deactivate completion objects
        foreach (GameObject obj in objectsToDeactivateOnCompletion)
        {
            if (obj != null)
                obj.SetActive(false);
        }

        Debug.Log("All targets have been scanned!");
    }

    void OnNotAllTargetsScanned()
    {
        // Reverse the completion state if not all targets are scanned anymore
        foreach (GameObject obj in objectsToActivateOnCompletion)
        {
            if (obj != null)
                obj.SetActive(false);
        }

        foreach (GameObject obj in objectsToDeactivateOnCompletion)
        {
            if (obj != null)
                obj.SetActive(true);
        }
    }
}
