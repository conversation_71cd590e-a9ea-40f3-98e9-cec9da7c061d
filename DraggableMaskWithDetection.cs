using UnityEngine;
using UnityEngine.EventSystems;

public class DraggableMaskWithDetection : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IDragHandler
{
    public RectTransform maskRect;        // The circular draggable mask
    public RectTransform[] targets;       // The small UI elements to detect
    public GameObject[] relatedObjects;   // Objects to activate based on overlap

    private Canvas canvas;

    void Start()
    {
        canvas = GetComponentInParent<Canvas>();
    }

    public void OnDrag(PointerEventData eventData)
    {
        // Convert drag delta to UI position
        Vector2 delta;
        RectTransformUtility.ScreenPointToLocalPointInRectangle(
            (RectTransform)canvas.transform,
            eventData.position,
            canvas.worldCamera,
            out delta
        );

        maskRect.anchoredPosition += eventData.delta;

        CheckOverlap();
    }

    void CheckOverlap()
    {
        for (int i = 0; i < targets.Length; i++)
        {
            if (RectOverlaps(maskRect, targets[i]))
                relatedObjects[i].SetActive(true);
            else
                relatedObjects[i].SetActive(false);
        }
    }

    bool RectOverlaps(RectTransform a, RectTransform b)
    {
        Vector3[] cornersA = new Vector3[4];
        Vector3[] cornersB = new Vector3[4];

        a.GetWorldCorners(cornersA);
        b.GetWorldCorners(cornersB);

        Rect rectA = new Rect(cornersA[0], cornersA[2] - cornersA[0]);
        Rect rectB = new Rect(cornersB[0], cornersB[2] - cornersB[0]);

        return rectA.Overlaps(rectB);
    }
}
